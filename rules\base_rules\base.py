from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any

from models import PatientData, RuleResult


class BaseRule(ABC):
    """
    规则基类

    所有规则必须继承此基类并实现validate方法
    """

    # 定义数组字段列表
    ARRAY_FIELDS = [
        'yb_code',
        'diag_whole_code',
        'diag_code_prefix',
        'fee_whole_code',
        'fee_code_prefix'
    ]

    def __init__(self, rule_id: str, **kwargs):
        """
        初始化规则基类

        Args:
            rule_id: 规则ID
        """
        self.rule_id = rule_id
        # 使用 kwargs 捕获所有其他参数，以便子类可以定义自己的构造函数
        # 同时允许在不修改基类的情况下，从数据集中传递任意参数。
        for key, value in kwargs.items():
            if key in self.ARRAY_FIELDS:
                # 字符串转换为列表
                if isinstance(value, str):
                    setattr(self, key, self._convert_string_to_set(value))
                elif isinstance(value, list):
                    setattr(self, key, set(value))
            else:
                setattr(self, key, value)

    @classmethod
    def _convert_string_to_set(cls, value: str) -> set[str]:
        """将逗号分隔字符串转换为列表"""
        if not value:
            return set()
        return set(item.strip() for item in value.split(",") if item.strip())

    @abstractmethod
    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        验证患者数据是否符合规则。

        Args:
            patient_data: 包含患者信息的字典。

        Returns:
            如果触发规则，则返回一个 ValidationResult 实例，否则返回 None。
        """
        raise NotImplementedError

    def get_rule_info(self) -> dict[str, Any]:
        """获取规则信息"""
        return {
            "rule_id": self.rule_id,
            "rule_name": self.rule_name,
            "level1": self.level1,
            "level2": self.level2,
            "level3": self.level3,
        }

    def _trans_timestamp_to_date(self, timestamp: int) -> str:
        """将时间戳转换为日期字符串"""
        return datetime.fromtimestamp(timestamp).strftime("%Y%m%d")

    def __repr__(self):
        return f"<{self.__class__.__name__}(rule_id='{self.rule_id}')>"
