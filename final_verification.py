#!/usr/bin/env python3
"""
最终验证脚本 - 验证所有修改是否正确
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_database_model_integration():
    """测试数据库模型集成"""
    print("测试1: 数据库模型集成")

    try:
        from core.db_session import get_session_factory
        from models.database import RuleDetail, RuleTemplate, RuleTemplateStatusEnum

        session_factory = get_session_factory()
        session = session_factory()

        try:
            # 创建测试规则模板
            template = RuleTemplate(
                rule_key="test_integration",
                rule_type="测试规则",
                name="集成测试规则",
                description="用于集成测试的规则模板",
                status=RuleTemplateStatusEnum.NEW,
            )
            session.add(template)
            session.commit()

            # 创建规则明细，测试数组字段
            detail = RuleDetail(
                rule_id="INTEGRATION_TEST",
                rule_key="test_integration",
                rule_name="集成测试规则明细",
                level1="用药安全",
                level2="适应症限制",
                level3="年龄限制",
                error_reason="集成测试错误原因",
                degree="严重",
                reference="集成测试质控依据",
                detail_position="集成测试位置",
                prompted_fields1="test_code",
                type="药品规则",
                pos="门诊",
                applicableArea="全国",
                default_use="是",
                start_date="2024-01-01",
                end_date="2024-12-31",
                # 测试数组字段 - 设置为列表
                yb_code=["INT_YB_001", "INT_YB_002"],
                diag_whole_code=["INT_DIAG_001"],
                diag_code_prefix=["INT_PREFIX"],
                fee_whole_code=["INT_FEE_001"],
                fee_code_prefix=["INT_FEE_PREFIX"],
            )
            session.add(detail)
            session.commit()
            session.refresh(detail)

            # 验证数组字段的属性访问器
            assert detail.yb_code == ["INT_YB_001", "INT_YB_002"], f"yb_code错误: {detail.yb_code}"
            assert detail.diag_whole_code == ["INT_DIAG_001"], f"diag_whole_code错误: {detail.diag_whole_code}"

            # 验证数据库存储格式
            assert detail._yb_code == "INT_YB_001,INT_YB_002", f"数据库存储错误: {detail._yb_code}"

            # 重新查询验证持久化
            queried_detail = session.query(RuleDetail).filter(RuleDetail.rule_id == "INTEGRATION_TEST").first()
            assert queried_detail.yb_code == ["INT_YB_001", "INT_YB_002"], "重新查询后数据不一致"

            print("✅ 测试1通过: 数据库模型集成正常")
            return True

        finally:
            # 清理测试数据
            session.query(RuleDetail).filter(RuleDetail.rule_key == "test_integration").delete()
            session.query(RuleTemplate).filter(RuleTemplate.rule_key == "test_integration").delete()
            session.commit()
            session.close()

    except Exception as e:
        print(f"❌ 测试1异常: {e}")
        return False


def test_api_response_format():
    """测试API响应格式"""
    print("\n测试2: API响应格式")

    try:
        from api.routers.master.rule_details import _convert_rule_detail_to_response
        from models.api import RuleDetailResponse

        # 创建模拟数据库对象
        from models.database import RuleDetail, RuleDetailStatusEnum

        mock_detail = RuleDetail(
            id=1,
            rule_id="API_TEST",
            rule_key="test_api",
            rule_name="API测试规则",
            level1="用药安全",
            level2="适应症限制",
            level3="年龄限制",
            error_reason="API测试错误原因",
            degree="严重",
            reference="API测试参考",
            detail_position="API测试位置",
            prompted_fields1="test_field",
            type="药品规则",
            pos="门诊",
            applicableArea="全国",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31",
            status=RuleDetailStatusEnum.ACTIVE,  # 添加状态字段
        )

        # 设置数组字段
        mock_detail.yb_code = ["API_YB_001", "API_YB_002"]
        mock_detail.diag_whole_code = ["API_DIAG_001"]

        # 测试转换函数
        response = _convert_rule_detail_to_response(mock_detail)

        # 验证响应类型
        assert isinstance(response, RuleDetailResponse), f"响应类型错误: {type(response)}"

        # 验证数组字段格式
        assert response.yb_code == ["API_YB_001", "API_YB_002"], f"响应yb_code错误: {response.yb_code}"
        assert response.diag_whole_code == ["API_DIAG_001"], f"响应diag_whole_code错误: {response.diag_whole_code}"

        print("✅ 测试2通过: API响应格式正确")
        return True

    except Exception as e:
        print(f"❌ 测试2异常: {e}")
        return False


def test_pydantic_validation():
    """测试Pydantic验证"""
    print("\n测试3: Pydantic验证")

    try:
        from pydantic import ValidationError

        from models.api import CreateRuleDetailRequest, RuleDetailResponse

        # 测试创建请求（列表格式）
        create_data = {
            "rule_id": "PYDANTIC_TEST",
            "rule_key": "test_pydantic",
            "rule_name": "Pydantic测试规则",
            "level1": "用药安全",
            "level2": "适应症限制",
            "level3": "年龄限制",
            "error_reason": "Pydantic测试错误原因",
            "degree": "严重",
            "reference": "Pydantic测试参考",
            "detail_position": "Pydantic测试位置",
            "prompted_fields1": "test_field",
            "type": "药品规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": ["PYD_YB_001", "PYD_YB_002"],  # 列表格式
            "diag_whole_code": ["PYD_DIAG_001"],
        }

        create_request = CreateRuleDetailRequest(**create_data)
        assert create_request.yb_code == ["PYD_YB_001", "PYD_YB_002"], "创建请求验证失败"

        # 测试响应模型（列表格式）
        response_data = create_data.copy()
        response_data.update(
            {"id": 1, "status": "ACTIVE", "created_at": "2024-01-01T00:00:00", "updated_at": "2024-01-01T00:00:00"}
        )

        response = RuleDetailResponse(**response_data)
        assert response.yb_code == ["PYD_YB_001", "PYD_YB_002"], "响应模型验证失败"

        print("✅ 测试3通过: Pydantic验证正常")
        return True

    except ValidationError as e:
        print(f"❌ 测试3验证错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试3异常: {e}")
        return False


def main():
    """主函数"""
    print("开始最终验证...")
    print("=" * 60)

    tests = [test_database_model_integration, test_api_response_format, test_pydantic_validation]

    passed = 0
    total = len(tests)

    for test_func in tests:
        if test_func():
            passed += 1

    print("\n" + "=" * 60)
    print(f"最终验证结果: {passed}/{total} 测试通过")

    if passed == total:
        print("🎉 所有验证通过！数组字段类型统一修复成功！")
        print("\n✅ 修复总结:")
        print("   - API模型统一为列表类型")
        print("   - 数据库模型添加属性访问器")
        print("   - 响应转换函数修复")
        print("   - 转换工具函数添加")
        print("   - 测试用例更新")
        return True
    else:
        print("⚠️  部分验证失败，请检查修改。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
