#!/usr/bin/env python3
"""
验证数组字段修复的脚本
运行此脚本来验证修复是否成功
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_model_validation():
    """测试API模型验证"""
    print("测试1: API模型验证")

    try:
        from pydantic import ValidationError

        from models.api import RuleDetailResponse

        # 测试列表格式数据
        test_data = {
            "id": 1,
            "rule_id": "TEST_001",
            "rule_key": "test_rule",
            "rule_name": "测试规则",
            "level1": "用药安全",
            "level2": "适应症限制", 
            "level3": "年龄限制",
            "error_reason": "测试错误原因",
            "degree": "严重",
            "reference": "测试参考",
            "detail_position": "测试位置",
            "prompted_fields1": "test_field",
            "type": "药品规则",
            "pos": "门诊",
            "applicableArea": "全国",
            "default_use": "是",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yb_code": ["A001", "A002"],  # 列表格式
            "diag_whole_code": ["K02.1"],
            "status": "ACTIVE"
        }

        response = RuleDetailResponse(**test_data)
        print("✅ 测试1通过: API模型接受列表格式")
        print(f"   yb_code: {response.yb_code}")
        return True

    except ValidationError as e:
        print(f"❌ 测试1失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试1异常: {e}")
        return False


def test_conversion_functions():
    """测试转换函数"""
    print("\n测试2: 转换函数")

    try:
        from services.unified_data_mapping_engine import convert_db_string_to_list, convert_list_to_db_string

        # 测试字符串到列表
        test_cases = [
            ("A001,A002,A003", ["A001", "A002", "A003"]),
            ("A001, , A003, ", ["A001", "A003"]),
            ("", []),
            (None, []),
            ("A001", ["A001"])
        ]

        for input_val, expected in test_cases:
            result = convert_db_string_to_list(input_val)
            if result != expected:
                print(f"❌ 字符串转列表失败: {input_val} -> {result}, 期望: {expected}")
                return False

        # 测试列表到字符串
        test_cases = [
            (["A001", "A002", "A003"], "A001,A002,A003"),
            ([], ""),
            (None, ""),
            (["A001"], "A001")
        ]

        for input_val, expected in test_cases:
            result = convert_list_to_db_string(input_val)
            if result != expected:
                print(f"❌ 列表转字符串失败: {input_val} -> {result}, 期望: {expected}")
                return False

        print("✅ 测试2通过: 转换函数工作正常")
        return True

    except Exception as e:
        print(f"❌ 测试2异常: {e}")
        return False


def test_database_model_properties():
    """测试数据库模型属性"""
    print("\n测试3: 数据库模型属性")

    try:
        from models.database import RuleDetail

        # 创建测试对象
        detail = RuleDetail(
            rule_id="TEST_001",
            rule_key="test_rule",
            rule_name="测试规则",
            level1="用药安全",
            level2="适应症限制",
            level3="年龄限制",
            error_reason="测试错误原因",
            degree="严重",
            reference="测试参考",
            detail_position="测试位置",
            prompted_fields1="test_field",
            type="药品规则",
            pos="门诊",
            applicableArea="全国",
            default_use="是",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )

        # 测试设置列表值
        detail.yb_code = ["A001", "A002", "A003"]
        detail.diag_whole_code = ["K02.1", "K02.2"]

        # 验证属性访问器
        if detail.yb_code != ["A001", "A002", "A003"]:
            print(f"❌ yb_code属性访问器失败: {detail.yb_code}")
            return False

        if detail.diag_whole_code != ["K02.1", "K02.2"]:
            print(f"❌ diag_whole_code属性访问器失败: {detail.diag_whole_code}")
            return False

        # 验证数据库存储格式
        if detail._yb_code != "A001,A002,A003":
            print(f"❌ 数据库存储格式错误: {detail._yb_code}")
            return False

        print("✅ 测试3通过: 数据库模型属性工作正常")
        return True

    except Exception as e:
        print(f"❌ 测试3异常: {e}")
        return False


def main():
    """主函数"""
    print("开始验证数组字段修复...")
    print("=" * 50)

    tests = [
        test_api_model_validation,
        test_conversion_functions,
        test_database_model_properties
    ]

    passed = 0
    total = len(tests)

    for test_func in tests:
        if test_func():
            passed += 1

    print("\n" + "=" * 50)
    print(f"验证结果: {passed}/{total} 测试通过")

    if passed == total:
        print("🎉 所有验证通过！可以继续下一步修改。")
        return True
    else:
        print("⚠️  部分验证失败，请检查修改是否正确。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
