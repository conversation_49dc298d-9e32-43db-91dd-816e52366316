"""
字段元数据初始化服务
负责为22种规则类型创建完整的字段元数据，支持动态Excel模板生成和数据校验功能
"""

import importlib
import inspect
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any

from sqlalchemy import and_, func
from sqlalchemy.orm import Session

from models.database import FieldTypeEnum, RuleFieldMetadata, RuleTemplate, RuleTemplateStatusEnum
from rules.base_rules.base import BaseRule
from tools.field_mapping_manager import FieldMappingManager

logger = logging.getLogger(__name__)


class RuleFieldMetadataInitializerError(Exception):
    """字段元数据初始化相关异常"""

    def __init__(self, message: str, details: dict[str, Any] = None):
        super().__init__(message)
        self.details = details or {}


class InitializationStats:
    """初始化统计信息"""

    def __init__(self):
        self.start_time = datetime.now()
        self.end_time = None
        self.total_rule_types = 0
        self.created_templates = 0
        self.updated_templates = 0
        self.created_field_metadata = 0
        self.updated_field_metadata = 0
        self.errors = []
        self.warnings = []

    def finalize(self):
        """完成统计"""
        self.end_time = datetime.now()

    @property
    def duration(self) -> float:
        """获取执行时长（秒）"""
        end = self.end_time or datetime.now()
        return (end - self.start_time).total_seconds()

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_seconds": self.duration,
            "total_rule_types": self.total_rule_types,
            "created_templates": self.created_templates,
            "updated_templates": self.updated_templates,
            "created_field_metadata": self.created_field_metadata,
            "updated_field_metadata": self.updated_field_metadata,
            "total_errors": len(self.errors),
            "total_warnings": len(self.warnings),
            "errors": self.errors,
            "warnings": self.warnings,
        }


class FieldMetadataBuilder:
    """
    字段元数据构建器

    负责根据field_mapping.json配置构建具体的字段元数据记录。
    处理通用字段和特定字段的分类，设置正确的字段属性和Excel列顺序。
    """

    def __init__(self, field_mapping_manager: FieldMappingManager):
        """
        初始化字段元数据构建器

        Args:
            field_mapping_manager: 字段映射管理器
        """
        self.field_mapping_manager = field_mapping_manager
        logger.debug("FieldMetadataBuilder 初始化完成")

    def build_template_record(self, rule_key: str, rule_info: dict[str, Any]) -> RuleTemplate:
        """
        构建规则模板记录

        Args:
            rule_key: 规则键值
            rule_info: 规则信息字典

        Returns:
            RuleTemplate: 构建的规则模板记录

        Raises:
            ValueError: 当规则信息不完整时抛出
        """
        try:
            if not rule_key:
                raise ValueError("规则键值不能为空")

            if not isinstance(rule_info, dict):
                raise ValueError("规则信息必须是字典类型")

            template = RuleTemplate(
                rule_key=rule_key,
                rule_type=rule_key,  # 使用rule_key作为rule_type
                name=rule_info.get("name", rule_key),
                description=rule_info.get('rule_desc', rule_key),
                status=RuleTemplateStatusEnum.NEW,
            )

            logger.debug(f"构建规则模板记录: {rule_key}")
            return template

        except Exception as e:
            logger.error(f"构建规则模板记录失败 {rule_key}: {e}")
            raise ValueError(f"构建规则模板记录失败: {str(e)}") from e

    def build_field_metadata_records(
        self, rule_key: str, field_list: list[str], required_fields: list[str] = None
    ) -> list[RuleFieldMetadata]:
        """
        构建字段元数据记录列表

        Args:
            rule_key: 规则键值
            field_list: 字段名称列表
            required_fields: 必填字段列表，默认为空列表

        Returns:
            List[RuleFieldMetadata]: 构建的字段元数据记录列表
        """
        if not rule_key:
            raise ValueError("规则键值不能为空")

        if not field_list:
            logger.warning(f"规则类型 {rule_key} 没有字段定义")
            return []

        required_fields = required_fields or []
        metadata_records = []

        for field_name in field_list:
            try:
                # 判断是否为必填字段
                is_required = field_name in required_fields

                # 构建单个字段元数据记录
                metadata_record = self._build_single_field_metadata(rule_key, field_name, is_required)

                if metadata_record:
                    metadata_records.append(metadata_record)
                else:
                    logger.warning(f"跳过字段 {rule_key}.{field_name}，无法构建元数据记录")

            except Exception as e:
                logger.error(f"构建字段元数据失败 {rule_key}.{field_name}: {e}")
                continue

        logger.debug(f"为规则类型 {rule_key} 构建了 {len(metadata_records)} 个字段元数据记录")
        return metadata_records

    def _build_single_field_metadata(
        self, rule_key: str, field_name: str, is_required: bool
    ) -> RuleFieldMetadata | None:
        """
        构建单个字段的元数据记录

        Args:
            rule_key: 规则键值
            field_name: 字段名称
            is_required: 是否必填

        Returns:
            Optional[RuleFieldMetadata]: 构建的字段元数据记录，失败时返回None
        """
        try:
            # 获取字段定义
            field_def = self.field_mapping_manager.get_field_definition(field_name)
            if not field_def:
                logger.warning(f"字段 {field_name} 未在配置中找到定义")
                return None

            # 确定字段属性
            field_properties = self._determine_field_properties(field_name, field_def)

            # 构建验证规则JSON
            validation_rules = field_def.get("validation_rules", [])
            validation_rule_json = json.dumps(validation_rules) if validation_rules else None

            return RuleFieldMetadata(
                rule_key=rule_key,
                field_name=field_name,
                field_type=field_properties["field_type"],
                is_required=is_required,
                is_fixed_field=field_properties["is_fixed_field"],
                display_name=field_def.get("chinese_name", field_name),
                description=field_def.get("description", ""),
                validation_rule=validation_rule_json,
                default_value=field_def.get("default_value"),
                excel_column_order=field_properties["excel_order"],
            )

        except Exception as e:
            logger.error(f"构建字段元数据记录失败 {rule_key}.{field_name}: {e}")
            return None

    def _determine_field_properties(self, field_name: str, field_def: dict[str, Any]) -> dict[str, Any]:
        """
        确定字段属性

        Args:
            field_name: 字段名称
            field_def: 字段定义字典

        Returns:
            Dict[str, Any]: 包含字段属性的字典
                - field_type: 字段类型枚举
                - is_fixed_field: 是否为固定字段
                - excel_order: Excel列顺序
        """
        try:
            # 确定字段类型
            field_type = self._map_field_type(field_def.get("data_type", "string"))

            # 确定是否为固定字段（通用字段为固定字段）
            is_fixed_field = self._is_fixed_field(field_name)

            # 计算Excel列顺序
            excel_order = self._calculate_excel_order(field_name, is_fixed_field, field_def)

            return {"field_type": field_type, "is_fixed_field": is_fixed_field, "excel_order": excel_order}

        except Exception as e:
            logger.error(f"确定字段属性失败 {field_name}: {e}")
            # 返回默认值
            return {"field_type": FieldTypeEnum.string, "is_fixed_field": False, "excel_order": 999}

    def _map_field_type(self, data_type: str) -> FieldTypeEnum:
        """
        映射数据类型到枚举值

        Args:
            data_type: 数据类型字符串

        Returns:
            FieldTypeEnum: 对应的字段类型枚举
        """
        type_mapping = {
            "string": FieldTypeEnum.string,
            "text": FieldTypeEnum.string,
            "integer": FieldTypeEnum.integer,
            "array": FieldTypeEnum.array,
            "boolean": FieldTypeEnum.boolean,
        }
        return type_mapping.get(data_type.lower(), FieldTypeEnum.string)

    def _is_fixed_field(self, field_name: str) -> bool:
        """
        判断是否为固定字段（通用字段）

        Args:
            field_name: 字段名称

        Returns:
            bool: True表示固定字段，False表示特定字段
        """
        try:
            config = self.field_mapping_manager.config
            if not config:
                return False

            # 检查是否在common_fields中（通用字段为固定字段）
            common_fields = config.get("field_definitions", {}).get("common_fields", {})
            return field_name in common_fields

        except Exception as e:
            logger.warning(f"判断固定字段失败 {field_name}: {e}")
            return False

    def _calculate_excel_order(self, field_name: str, is_fixed_field: bool, field_def: dict[str, Any]) -> int:
        """
        计算Excel列顺序，支持自定义排序

        Args:
            field_name: 字段名称
            is_fixed_field: 是否为固定字段
            field_def: 字段定义字典

        Returns:
            int: Excel列顺序号
        """
        try:
            # 首先检查字段定义中是否有自定义的excel_order
            if "excel_order" in field_def:
                return field_def["excel_order"]

            # 使用默认排序逻辑
            config = self.field_mapping_manager.config
            if not config:
                return 999  # 默认值

            if is_fixed_field:
                # 通用字段：按在common_fields中的顺序排列（1-25）
                common_fields = config.get("field_definitions", {}).get("common_fields", {})
                field_names = list(common_fields.keys())
                try:
                    return field_names.index(field_name) + 1
                except ValueError:
                    return 25  # 如果不在列表中，放在通用字段末尾
            else:
                # 特定字段：从26开始
                specific_fields = config.get("field_definitions", {}).get("specific_fields", {})
                field_names = list(specific_fields.keys())
                try:
                    return field_names.index(field_name) + 26
                except ValueError:
                    return 50  # 如果不在列表中，使用默认值

        except Exception as e:
            logger.warning(f"计算Excel列顺序失败 {field_name}: {e}")
            return 999  # 出错时使用默认值

    def validate_field_metadata_record(self, metadata: RuleFieldMetadata) -> tuple[bool, list[str]]:
        """
        验证字段元数据记录的有效性

        Args:
            metadata: 字段元数据记录

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []

        try:
            # 验证必填字段
            if not metadata.rule_key:
                errors.append("规则键值不能为空")

            if not metadata.field_name:
                errors.append("字段名称不能为空")

            if not metadata.display_name:
                errors.append("显示名称不能为空")

            # 验证Excel列顺序
            if metadata.excel_column_order is None or metadata.excel_column_order < 1:
                errors.append("Excel列顺序必须大于0")

            # 验证字段类型
            if not isinstance(metadata.field_type, FieldTypeEnum):
                errors.append("字段类型必须是有效的枚举值")

            # 验证验证规则JSON格式
            if metadata.validation_rule:
                try:
                    json.loads(metadata.validation_rule)
                except json.JSONDecodeError:
                    errors.append("验证规则必须是有效的JSON格式")

            return len(errors) == 0, errors

        except Exception as e:
            logger.error(f"验证字段元数据记录时出错: {e}")
            errors.append(f"验证过程出错: {str(e)}")
            return False, errors


class ValidationEngine:
    """
    数据验证引擎

    负责验证初始化数据的完整性和正确性。检查配置文件完整性、字段定义一致性、
    外键关联正确性和重复数据检测。
    """

    def __init__(self, field_mapping_manager: FieldMappingManager, session: Session = None):
        """
        初始化验证引擎

        Args:
            field_mapping_manager: 字段映射管理器
            session: 数据库会话，用于数据库相关验证
        """
        self.field_mapping_manager = field_mapping_manager
        self.session = session
        self.validation_errors = []
        self.validation_warnings = []

        logger.debug("ValidationEngine 初始化完成")

    def validate_config_completeness(self) -> tuple[bool, list[str]]:
        """
        验证field_mapping.json配置文件的完整性

        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []

        try:
            config = self.field_mapping_manager.config
            if not config:
                errors.append("配置文件未加载或为空")
                return False, errors

            # 验证必要的顶级键
            required_keys = ["metadata", "rule_type_mappings", "field_definitions"]
            for key in required_keys:
                if key not in config:
                    errors.append(f"配置文件缺少必要的键: {key}")

            # 验证版本信息
            if "metadata" in config:
                metadata = config["metadata"]
                version = metadata.get("version")
                if not isinstance(version, str) or not version:
                    errors.append("版本信息格式不正确")

            # 验证规则类型映射
            if "rule_type_mappings" in config:
                rule_mappings = config["rule_type_mappings"]
                if not isinstance(rule_mappings, dict):
                    errors.append("rule_type_mappings必须是字典类型")
                elif len(rule_mappings) == 0:
                    errors.append("rule_type_mappings不能为空")
                else:
                    # 验证每个规则类型的结构
                    for rule_key, rule_info in rule_mappings.items():
                        if not isinstance(rule_info, dict):
                            errors.append(f"规则类型 {rule_key} 的配置必须是字典类型")
                            continue

                        # 验证必要字段
                        if "name" not in rule_info:
                            errors.append(f"规则类型 {rule_key} 缺少name字段")

                        if "required_fields" not in rule_info:
                            errors.append(f"规则类型 {rule_key} 缺少required_fields字段")
                        elif not isinstance(rule_info["required_fields"], list):
                            errors.append(f"规则类型 {rule_key} 的required_fields必须是列表类型")

                        if "optional_fields" not in rule_info:
                            errors.append(f"规则类型 {rule_key} 缺少optional_fields字段")
                        elif not isinstance(rule_info["optional_fields"], list):
                            errors.append(f"规则类型 {rule_key} 的optional_fields必须是列表类型")

            # 验证字段定义
            if "field_definitions" in config:
                field_defs = config["field_definitions"]
                if not isinstance(field_defs, dict):
                    errors.append("field_definitions必须是字典类型")
                else:
                    # 验证common_fields和specific_fields
                    if "common_fields" not in field_defs:
                        errors.append("field_definitions缺少common_fields")
                    elif not isinstance(field_defs["common_fields"], dict):
                        errors.append("common_fields必须是字典类型")

                    if "specific_fields" not in field_defs:
                        errors.append("field_definitions缺少specific_fields")
                    elif not isinstance(field_defs["specific_fields"], dict):
                        errors.append("specific_fields必须是字典类型")

            logger.info(f"配置文件完整性验证完成，发现 {len(errors)} 个错误")
            return len(errors) == 0, errors

        except Exception as e:
            error_msg = f"验证配置文件完整性时出错: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            return False, errors

    def validate_field_consistency(self) -> tuple[bool, list[str]]:
        """
        检查字段定义的一致性

        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []

        try:
            config = self.field_mapping_manager.config
            if not config:
                errors.append("配置文件未加载")
                return False, errors

            rule_mappings = config.get("rule_type_mappings", {})
            field_defs = config.get("field_definitions", {})
            common_fields = field_defs.get("common_fields", {})
            specific_fields = field_defs.get("specific_fields", {})

            # 收集所有定义的字段
            all_defined_fields = set(common_fields.keys()) | set(specific_fields.keys())

            # 收集所有使用的字段
            all_used_fields = set()
            for _, rule_info in rule_mappings.items():
                required_fields = rule_info.get("required_fields", [])
                optional_fields = rule_info.get("optional_fields", [])
                all_used_fields.update(required_fields)
                all_used_fields.update(optional_fields)

            # 检查使用的字段是否都有定义
            undefined_fields = all_used_fields - all_defined_fields
            if undefined_fields:
                for field in undefined_fields:
                    errors.append(f"字段 {field} 被使用但未在field_definitions中定义")

            # 检查定义的字段是否都被使用
            unused_fields = all_defined_fields - all_used_fields
            if unused_fields:
                for field in unused_fields:
                    self.validation_warnings.append(f"字段 {field} 已定义但未被任何规则类型使用")

            # 验证字段定义的结构
            for field_name, field_def in {**common_fields, **specific_fields}.items():
                if not isinstance(field_def, dict):
                    errors.append(f"字段 {field_name} 的定义必须是字典类型")
                    continue

                # 验证必要属性
                required_attrs = ["chinese_name", "data_type"]
                for attr in required_attrs:
                    if attr not in field_def:
                        errors.append(f"字段 {field_name} 缺少必要属性: {attr}")

                # 验证数据类型
                if "data_type" in field_def:
                    valid_types = ["string", "integer", "array", "boolean", "text"]
                    if field_def["data_type"] not in valid_types:
                        errors.append(
                            f"字段 {field_name} 的数据类型 {field_def['data_type']} 不在支持的类型中: {valid_types}"
                        )

            logger.info(f"字段一致性验证完成，发现 {len(errors)} 个错误，{len(self.validation_warnings)} 个警告")
            return len(errors) == 0, errors

        except Exception as e:
            error_msg = f"验证字段一致性时出错: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            return False, errors

    def validate_foreign_key_integrity(self) -> tuple[bool, list[str]]:
        """
        验证外键关联的正确性

        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []

        if not self.session:
            errors.append("数据库会话未提供，无法验证外键关联")
            return False, errors

        try:
            # 验证rule_field_metadata表中的rule_key是否都在rule_template表中存在
            orphaned_metadata = (
                self.session.query(RuleFieldMetadata)
                .filter(~RuleFieldMetadata.rule_key.in_(self.session.query(RuleTemplate.rule_key)))
                .all()
            )

            if orphaned_metadata:
                for metadata in orphaned_metadata:
                    errors.append(
                        f"字段元数据记录 {metadata.rule_key}.{metadata.field_name} 的rule_key在rule_template表中不存在"
                    )

            # 验证rule_template表中的rule_key是否都有对应的字段元数据
            templates_without_metadata = (
                self.session.query(RuleTemplate)
                .filter(~RuleTemplate.rule_key.in_(self.session.query(RuleFieldMetadata.rule_key).distinct()))
                .all()
            )

            if templates_without_metadata:
                for template in templates_without_metadata:
                    self.validation_warnings.append(f"规则模板 {template.rule_key} 没有对应的字段元数据记录")

            logger.info(f"外键关联验证完成，发现 {len(errors)} 个错误，{len(self.validation_warnings)} 个警告")
            return len(errors) == 0, errors

        except Exception as e:
            error_msg = f"验证外键关联时出错: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            return False, errors

    def detect_duplicate_data(self) -> tuple[bool, list[str]]:
        """
        检测重复数据

        Returns:
            Tuple[bool, List[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []

        if not self.session:
            errors.append("数据库会话未提供，无法检测重复数据")
            return False, errors

        try:
            # 检测重复的规则模板
            duplicate_templates = (
                self.session.query(RuleTemplate.rule_key, func.count(RuleTemplate.rule_key))
                .group_by(RuleTemplate.rule_key)
                .having(func.count(RuleTemplate.rule_key) > 1)
                .all()
            )

            if duplicate_templates:
                for rule_key, count in duplicate_templates:
                    errors.append(f"规则模板 {rule_key} 存在 {count} 条重复记录")

            # 检测重复的字段元数据
            duplicate_metadata = (
                self.session.query(RuleFieldMetadata.rule_key, RuleFieldMetadata.field_name, func.count())
                .group_by(RuleFieldMetadata.rule_key, RuleFieldMetadata.field_name)
                .having(func.count() > 1)
                .all()
            )

            if duplicate_metadata:
                for rule_key, field_name, count in duplicate_metadata:
                    errors.append(f"字段元数据 {rule_key}.{field_name} 存在 {count} 条重复记录")

            # 检测Excel列顺序冲突
            excel_order_conflicts = (
                self.session.query(RuleFieldMetadata.rule_key, RuleFieldMetadata.excel_column_order, func.count())
                .group_by(RuleFieldMetadata.rule_key, RuleFieldMetadata.excel_column_order)
                .having(func.count() > 1)
                .all()
            )

            if excel_order_conflicts:
                for rule_key, excel_order, count in excel_order_conflicts:
                    self.validation_warnings.append(
                        f"规则类型 {rule_key} 的Excel列顺序 {excel_order} 被 {count} 个字段使用"
                    )

            logger.info(f"重复数据检测完成，发现 {len(errors)} 个错误，{len(self.validation_warnings)} 个警告")
            return len(errors) == 0, errors

        except Exception as e:
            error_msg = f"检测重复数据时出错: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            return False, errors

    def generate_validation_report(self) -> dict[str, Any]:
        """
        生成验证报告

        Returns:
            Dict[str, Any]: 结构化的验证报告
        """
        try:
            # 执行所有验证
            config_valid, config_errors = self.validate_config_completeness()
            field_valid, field_errors = self.validate_field_consistency()

            # 数据库相关验证（如果有会话）
            fk_valid, fk_errors = True, []
            duplicate_valid, duplicate_errors = True, []

            if self.session:
                fk_valid, fk_errors = self.validate_foreign_key_integrity()
                duplicate_valid, duplicate_errors = self.detect_duplicate_data()

            # 汇总结果
            all_errors = config_errors + field_errors + fk_errors + duplicate_errors
            overall_valid = config_valid and field_valid and fk_valid and duplicate_valid

            report = {
                "timestamp": datetime.now().isoformat(),
                "overall_status": "PASS" if overall_valid else "FAIL",
                "total_errors": len(all_errors),
                "total_warnings": len(self.validation_warnings),
                "validation_results": {
                    "config_completeness": {"status": "PASS" if config_valid else "FAIL", "errors": config_errors},
                    "field_consistency": {"status": "PASS" if field_valid else "FAIL", "errors": field_errors},
                    "foreign_key_integrity": {
                        "status": "PASS" if fk_valid else "FAIL",
                        "errors": fk_errors,
                        "enabled": self.session is not None,
                    },
                    "duplicate_detection": {
                        "status": "PASS" if duplicate_valid else "FAIL",
                        "errors": duplicate_errors,
                        "enabled": self.session is not None,
                    },
                },
                "warnings": self.validation_warnings,
                "recommendations": self._generate_recommendations(all_errors),
            }

            logger.info(f"验证报告生成完成，总体状态: {report['overall_status']}")
            return report

        except Exception as e:
            error_msg = f"生成验证报告时出错: {str(e)}"
            logger.error(error_msg)
            return {
                "timestamp": datetime.now().isoformat(),
                "overall_status": "ERROR",
                "error": error_msg,
                "total_errors": 1,
                "total_warnings": 0,
            }

    def _generate_recommendations(self, errors: list[str]) -> list[str]:
        """
        根据错误生成修复建议

        Args:
            errors: 错误信息列表

        Returns:
            List[str]: 修复建议列表
        """
        recommendations = []

        # 根据错误类型生成建议
        for error in errors:
            if "配置文件" in error:
                recommendations.append("检查field_mapping.json文件的格式和完整性")
            elif "字段定义" in error:
                recommendations.append("检查字段定义的结构和必要属性")
            elif "外键" in error:
                recommendations.append("确保rule_template和rule_field_metadata表的数据一致性")
            elif "重复" in error:
                recommendations.append("清理重复的数据记录")

        # 去重并返回
        return list(set(recommendations))


class RuleFieldMetadataInitializer:
    """
    字段元数据初始化服务类

    负责读取field_mapping.json配置并初始化rule_template和rule_field_metadata表数据。
    集成现有FieldMappingManager工具，实现配置驱动的元数据管理。
    """

    def __init__(self, session_factory, field_mapping_manager: FieldMappingManager = None):
        """
        初始化字段元数据初始化器

        Args:
            session_factory: 数据库会话工厂
            field_mapping_manager: 字段映射管理器，如果为None则创建默认实例
        """
        self.session_factory = session_factory
        self.field_mapping_manager = field_mapping_manager or FieldMappingManager()
        self.stats = InitializationStats()

        # 初始化字段元数据构建器
        self.field_metadata_builder = FieldMetadataBuilder(self.field_mapping_manager)

        # 初始化验证引擎
        self.validation_engine = None  # 将在需要时初始化

        logger.info("RuleFieldMetadataInitializer 初始化完成")

    def initialize_all_metadata(self, mode: str = "full") -> dict[str, Any]:
        """
        初始化所有字段元数据

        Args:
            mode: 初始化模式，'full'=完全重建，'incremental'=增量更新

        Returns:
            Dict: 初始化结果统计

        Raises:
            RuleFieldMetadataInitializerError: 初始化失败时抛出
        """
        logger.info(f"开始字段元数据初始化，模式: {mode}")

        if mode not in ["full", "incremental"]:
            raise RuleFieldMetadataInitializerError(f"不支持的初始化模式: {mode}")

        try:
            with self.session_factory() as session:
                # 获取规则类型映射
                rule_type_mappings = self._get_rule_type_mappings()
                self.stats.total_rule_types = len(rule_type_mappings)

                logger.info(f"发现 {self.stats.total_rule_types} 种规则类型")

                # 1. 创建或更新规则模板
                self._create_rule_templates(session, rule_type_mappings, mode)

                # 2. 创建或更新字段元数据
                self._create_field_metadata(session, rule_type_mappings, mode)

                # 3. 验证初始化结果
                self._validate_initialization(session)

                # 提交事务
                session.commit()

                self.stats.finalize()
                logger.info(f"字段元数据初始化完成，耗时: {self.stats.duration:.2f}秒")

                return self.stats.to_dict()

        except Exception as e:
            logger.error(f"字段元数据初始化失败: {e}", exc_info=True)
            self.stats.errors.append(f"初始化失败: {str(e)}")
            self.stats.finalize()
            raise RuleFieldMetadataInitializerError(f"初始化失败: {str(e)}", {"stats": self.stats.to_dict()}) from e

    def _get_rule_type_mappings(self) -> dict[str, Any]:
        """获取规则类型映射配置，优先从规则类实际定义中提取"""
        try:
            # 首先尝试从实际规则类中提取字段信息
            rule_mappings = self._extract_rule_mappings_from_classes()

            if rule_mappings:
                logger.info(f"从规则类中提取了 {len(rule_mappings)} 种规则类型映射")
                return rule_mappings

            # 如果提取失败，回退到配置文件
            logger.warning("从规则类提取字段信息失败，回退到配置文件")
            config = self.field_mapping_manager.config
            if not config:
                raise RuleFieldMetadataInitializerError("字段映射配置未加载")

            rule_type_mappings = config.get("rule_type_mappings", {})
            if not rule_type_mappings:
                raise RuleFieldMetadataInitializerError("配置中未找到规则类型映射")

            logger.debug(f"从配置文件加载了 {len(rule_type_mappings)} 种规则类型映射")
            return rule_type_mappings

        except Exception as e:
            raise RuleFieldMetadataInitializerError(f"获取规则类型映射失败: {str(e)}") from e

    def _extract_rule_mappings_from_classes(self) -> dict[str, Any]:
        """从规则类的__init__方法中提取字段映射信息"""

        rule_mappings = {}
        rules_dir = Path("rules/base_rules")

        if not rules_dir.exists():
            logger.warning(f"规则目录不存在: {rules_dir}")
            return {}

        try:
            for rule_file in rules_dir.glob("*.py"):
                if rule_file.name.startswith("__") or rule_file.name == "base.py":
                    continue

                try:
                    # 动态导入规则模块
                    module_name = f"rules.base_rules.{rule_file.stem}"
                    module = importlib.import_module(module_name)

                    # 查找BaseRule的子类
                    for name, obj in inspect.getmembers(module, inspect.isclass):
                        if issubclass(obj, BaseRule) and obj is not BaseRule and hasattr(obj, "rule_key"):
                            rule_key = obj.rule_key
                            rule_name = getattr(obj, "rule_name", rule_key)

                            # 提取__init__方法的参数
                            init_signature = inspect.signature(obj.__init__)
                            parameters = list(init_signature.parameters.keys())

                            # 过滤掉self和rule_id参数
                            field_parameters = [p for p in parameters if p not in ["self", "rule_id"]]

                            # 根据参数类型注解判断必填和可选字段
                            required_fields = []
                            optional_fields = []

                            for param_name in field_parameters:
                                param = init_signature.parameters[param_name]
                                # 如果有默认值或类型注解包含None，则为可选字段
                                if param.default != inspect.Parameter.empty or (
                                    param.annotation != inspect.Parameter.empty and "None" in str(param.annotation)
                                ):
                                    optional_fields.append(param_name)
                                else:
                                    required_fields.append(param_name)

                            rule_mappings[rule_key] = {
                                "name": rule_name,
                                "required_fields": required_fields,
                                "optional_fields": optional_fields,
                                "source": "extracted_from_class",
                            }

                            logger.debug(
                                f"从规则类 {rule_key} 提取字段: 必填={len(required_fields)}, 可选={len(optional_fields)}"
                            )

                except Exception as e:
                    logger.warning(f"提取规则类 {rule_file.name} 的字段信息失败: {e}")
                    continue

            return rule_mappings

        except Exception as e:
            logger.error(f"从规则类提取字段映射失败: {e}")
            return {}

    def _create_rule_templates(self, session: Session, rule_type_mappings: dict[str, Any], mode: str):
        """创建或更新规则模板记录"""
        logger.info("开始创建规则模板记录")

        for rule_key, rule_info in rule_type_mappings.items():
            try:
                # 检查是否已存在
                existing_template = session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()

                if existing_template:
                    if mode == "incremental":
                        # 增量模式：更新现有记录
                        existing_template.name = rule_info.get("name", rule_key)
                        # existing_template.description = rule_info.get('rule_desc', rule_key)
                        existing_template.rule_type = rule_key  # 使用rule_key作为rule_type
                        self.stats.updated_templates += 1
                        logger.debug(f"更新规则模板: {rule_key}")
                    else:
                        # 完全重建模式：跳过已存在的记录
                        logger.debug(f"规则模板已存在，跳过: {rule_key}")
                        continue
                else:
                    # 使用FieldMetadataBuilder创建新记录
                    new_template = self.field_metadata_builder.build_template_record(rule_key, rule_info)
                    session.add(new_template)
                    self.stats.created_templates += 1
                    logger.debug(f"创建规则模板: {rule_key}")

            except Exception as e:
                error_msg = f"处理规则模板 {rule_key} 时出错: {str(e)}"
                logger.error(error_msg)
                self.stats.errors.append(error_msg)
                continue

        # 刷新会话以获取新创建的记录ID
        session.flush()
        logger.info(f"规则模板处理完成，创建: {self.stats.created_templates}, 更新: {self.stats.updated_templates}")

    def _create_field_metadata(self, session: Session, rule_type_mappings: dict[str, Any], mode: str):
        """创建或更新字段元数据记录"""
        logger.info("开始创建字段元数据记录")

        for rule_key, rule_info in rule_type_mappings.items():
            try:
                # 获取该规则类型的所有字段
                required_fields = rule_info.get("required_fields", [])
                optional_fields = rule_info.get("optional_fields", [])
                all_fields = required_fields + optional_fields

                logger.debug(f"处理规则类型 {rule_key}，字段数量: {len(all_fields)}")

                # 为每个字段创建元数据记录
                for field_name in all_fields:
                    self._create_single_field_metadata(
                        session, rule_key, field_name, is_required=(field_name in required_fields), mode=mode
                    )

            except Exception as e:
                error_msg = f"处理规则类型 {rule_key} 的字段元数据时出错: {str(e)}"
                logger.error(error_msg)
                self.stats.errors.append(error_msg)
                continue

        logger.info(
            f"字段元数据处理完成，创建: {self.stats.created_field_metadata}, 更新: {self.stats.updated_field_metadata}"
        )

    def _create_single_field_metadata(
        self, session: Session, rule_key: str, field_name: str, is_required: bool, mode: str
    ):
        """创建单个字段的元数据记录"""
        try:
            # 检查是否已存在
            existing_metadata = (
                session.query(RuleFieldMetadata)
                .filter(and_(RuleFieldMetadata.rule_key == rule_key, RuleFieldMetadata.field_name == field_name))
                .first()
            )

            if existing_metadata:
                if mode == "incremental":
                    # 增量模式：更新现有记录
                    self._update_field_metadata(existing_metadata, field_name, is_required)
                    self.stats.updated_field_metadata += 1
                else:
                    # 完全重建模式：跳过已存在的记录
                    return
            else:
                # 创建新记录
                new_metadata = self._build_field_metadata_record(rule_key, field_name, is_required)
                if new_metadata:
                    # 使用merge而不是add来避免重复键错误
                    session.merge(new_metadata)
                    self.stats.created_field_metadata += 1
                    logger.debug(f"创建字段元数据: {rule_key}.{field_name}")
                else:
                    error_msg = f"规则类型 {rule_key} 缺少必填字段 {field_name} 的元数据"
                    # logger.warning(error_msg)
                    self.stats.warnings.append(error_msg)

        except Exception as e:
            error_msg = f"处理字段 {rule_key}.{field_name} 时出错: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)

    def _build_field_metadata_record(
        self, rule_key: str, field_name: str, is_required: bool
    ) -> RuleFieldMetadata | None:
        """构建字段元数据记录"""
        try:
            # 使用FieldMetadataBuilder构建字段元数据记录
            metadata_record = self.field_metadata_builder._build_single_field_metadata(
                rule_key, field_name, is_required
            )

            if not metadata_record:
                warning_msg = f"字段 {field_name} 无法构建元数据记录，跳过"
                logger.warning(warning_msg)
                self.stats.warnings.append(warning_msg)
                return None

            # 验证构建的记录
            is_valid, errors = self.field_metadata_builder.validate_field_metadata_record(metadata_record)
            if not is_valid:
                error_msg = f"字段元数据记录验证失败 {rule_key}.{field_name}: {'; '.join(errors)}"
                logger.error(error_msg)
                self.stats.errors.append(error_msg)
                return None

            return metadata_record

        except Exception as e:
            logger.error(f"构建字段元数据记录失败 {rule_key}.{field_name}: {e}")
            return None

    def _update_field_metadata(self, metadata: RuleFieldMetadata, field_name: str, is_required: bool):
        """更新现有字段元数据记录"""
        try:
            # 从FieldMappingManager获取最新字段定义
            field_def = self.field_mapping_manager.get_field_definition(field_name)
            if field_def:
                metadata.field_type = self.field_metadata_builder._map_field_type(field_def.get("data_type", "string"))
                metadata.is_required = is_required
                metadata.is_fixed_field = self.field_metadata_builder._is_fixed_field(field_name)
                metadata.display_name = field_def.get("chinese_name", field_name)
                metadata.description = field_def.get("description", "")
                metadata.excel_column_order = self.field_metadata_builder._calculate_excel_order(
                    field_name, metadata.is_fixed_field, field_def
                )

                # 更新验证规则
                validation_rules = field_def.get("validation_rules", [])
                metadata.validation_rule = json.dumps(validation_rules) if validation_rules else None

                # 更新默认值
                metadata.default_value = field_def.get("default_value")

        except Exception as e:
            logger.error(f"更新字段元数据失败 {field_name}: {e}")

    def _validate_initialization(self, session: Session):
        """验证初始化结果"""
        logger.info("开始验证初始化结果")

        try:
            # 初始化验证引擎
            if not self.validation_engine:
                self.validation_engine = ValidationEngine(self.field_mapping_manager, session)
            else:
                self.validation_engine.session = session

            # 使用ValidationEngine进行全面验证
            validation_report = self.validation_engine.generate_validation_report()

            # 将验证结果合并到统计信息中
            if validation_report["total_errors"] > 0:
                for result in validation_report["validation_results"].values():
                    if result.get("errors"):
                        self.stats.errors.extend(result["errors"])

            if validation_report["total_warnings"] > 0:
                self.stats.warnings.extend(validation_report["warnings"])

            # 记录验证报告
            logger.info(f"验证报告: {validation_report['overall_status']}")
            logger.info(
                f"错误数量: {validation_report['total_errors']}, 警告数量: {validation_report['total_warnings']}"
            )

            # 额外的初始化特定验证
            self._validate_initialization_specific(session)

            logger.info("初始化结果验证完成")

        except Exception as e:
            error_msg = f"验证初始化结果时出错: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)

    def _validate_initialization_specific(self, session: Session):
        """初始化特定的验证逻辑"""
        try:
            # 1. 验证规则模板数量
            template_count = session.query(RuleTemplate).count()
            expected_count = self.stats.total_rule_types

            if template_count < expected_count:
                warning_msg = f"规则模板数量不足，期望: {expected_count}, 实际: {template_count}"
                logger.warning(warning_msg)
                self.stats.warnings.append(warning_msg)

            # 2. 验证字段元数据完整性
            metadata_count = session.query(RuleFieldMetadata).count()
            logger.info(f"字段元数据总数: {metadata_count}")

            # 3. 验证必填字段完整性
            self._validate_required_fields(session)

        except Exception as e:
            error_msg = f"初始化特定验证时出错: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)

    def _validate_required_fields(self, session: Session):
        """验证必填字段完整性"""
        try:
            rule_type_mappings = self._get_rule_type_mappings()

            for rule_key, rule_info in rule_type_mappings.items():
                required_fields = rule_info.get("required_fields", [])

                # 检查每个必填字段是否都有元数据记录
                for field_name in required_fields:
                    metadata_exists = (
                        session.query(RuleFieldMetadata)
                        .filter(
                            and_(
                                RuleFieldMetadata.rule_key == rule_key,
                                RuleFieldMetadata.field_name == field_name,
                                RuleFieldMetadata.is_required == True,
                            )
                        )
                        .first()
                    )

                    if not metadata_exists:
                        warning_msg = f"规则类型 {rule_key} 缺少必填字段 {field_name} 的元数据"
                        # logger.warning(warning_msg)
                        self.stats.warnings.append(warning_msg)

        except Exception as e:
            logger.error(f"验证必填字段时出错: {e}")

    def get_initialization_report(self) -> str:
        """获取初始化报告"""
        stats = self.stats.to_dict()

        report = f"""
字段元数据初始化报告
==================

执行时间: {stats['start_time']} - {stats['end_time']}
执行时长: {stats['duration_seconds']:.2f} 秒

统计信息:
- 规则类型总数: {stats['total_rule_types']}
- 创建模板数: {stats['created_templates']}
- 更新模板数: {stats['updated_templates']}
- 创建字段元数据: {stats['created_field_metadata']}
- 更新字段元数据: {stats['updated_field_metadata']}

问题统计:
- 错误数量: {stats['total_errors']}
- 警告数量: {stats['total_warnings']}
"""

        if stats["errors"]:
            report += "\n错误详情:\n"
            for i, error in enumerate(stats["errors"], 1):
                report += f"{i}. {error}\n"

        if stats["warnings"]:
            report += "\n警告详情:\n"
            for i, warning in enumerate(stats["warnings"], 1):
                report += f"{i}. {warning}\n"

        return report
