import hashlib
import importlib
import inspect
import re
from pathlib import Path

from loguru import logger
from sqlalchemy.orm import Session

from models.database import RuleTemplate, RuleTemplateStatusEnum
from rules.base_rules.base import BaseRule as BaseRuleLogic


class RuleChangeDetector:
    """
    Scans the rule definition files, detects changes, and syncs with the database.
    """

    def __init__(self, db_session: Session, rules_base_dir: str = "rules/base_rules"):
        self.db = db_session
        self.rules_base_dir = Path(rules_base_dir)

    def run(self):
        """
        Executes the full detection and synchronization process.
        """
        logger.info("Starting rule change detection process...")

        try:
            found_rules_keys = self._scan_and_update_rules()
            self._deprecate_old_rules(found_rules_keys)

            self.db.commit()

            # 统计结果
            total_templates = self.db.query(RuleTemplate).count()
            active_templates = (
                self.db.query(RuleTemplate).filter(RuleTemplate.status != RuleTemplateStatusEnum.DEPRECATED).count()
            )

            logger.info(
                f"Rule change detection process finished successfully. "
                f"Found {len(found_rules_keys)} rule files, "
                f"Total templates in DB: {total_templates}, "
                f"Active templates: {active_templates}"
            )

        except Exception as e:
            logger.error(f"Rule change detection process failed: {e}", exc_info=True)
            self.db.rollback()
            raise

    def _scan_and_update_rules(self) -> set[str]:
        """
        Scans rule files, updates or creates entries in the database.
        Returns a set of rule_keys found in the filesystem.
        """
        found_keys = set()
        for rule_file in self.rules_base_dir.glob("*.py"):
            if rule_file.name.startswith("__"):
                continue

            module_path = f"rules.base_rules.{rule_file.stem}"
            try:
                rule_key, rule_name, rule_desc = self._get_rule_info_from_source(rule_file)

                if not rule_key:
                    logger.warning(f"Could not find 'rule_key' in {rule_file}. Skipping.")
                    continue

                found_keys.add(rule_key)
                file_hash = self._calculate_file_hash(rule_file)

                db_rule = self.db.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()

                if not db_rule:
                    logger.info(f"New rule found: '{rule_key}'. Adding to database.")
                    new_rule = RuleTemplate(
                        rule_key=rule_key,
                        name=rule_name,
                        description=rule_desc,  # 添加描述信息
                        module_path=module_path,
                        file_hash=file_hash,
                        status=RuleTemplateStatusEnum.NEW,
                    )
                    # 使用 merge 而不是 add 来避免重复键错误
                    self.db.merge(new_rule)
                elif db_rule.file_hash != file_hash:
                    logger.info(f"Rule '{rule_key}' has changed. Updating hash and status.")
                    db_rule.file_hash = file_hash
                    db_rule.status = RuleTemplateStatusEnum.CHANGED
                    db_rule.name = rule_name  # Also update name in case it changed
                    db_rule.description = rule_desc  # 同时更新描述信息
                else:
                    # 即使文件哈希没有变化，也检查描述是否需要更新
                    # 这处理了描述信息之前为空的情况
                    current_description = db_rule.description or ""
                    if current_description.strip() != rule_desc.strip() and rule_desc:
                        logger.info(f"Rule '{rule_key}' description updated.")
                        db_rule.description = rule_desc

            except Exception as e:
                logger.error(f"Failed to process rule file {rule_file}: {e}")

        return found_keys

    def _deprecate_old_rules(self, found_keys: set[str]):
        """
        Marks rules that exist in DB but not in filesystem as DEPRECATED.
        """
        db_rules = self.db.query(RuleTemplate).filter(RuleTemplate.status != RuleTemplateStatusEnum.DEPRECATED).all()
        for db_rule in db_rules:
            if db_rule.rule_key not in found_keys:
                logger.info(f"Rule '{db_rule.rule_key}' no longer exists. Marking as DEPRECATED.")
                db_rule.status = RuleTemplateStatusEnum.DEPRECATED

    @staticmethod
    def _calculate_file_hash(file_path: Path) -> str:
        """Calculates the SHA-256 hash of a file."""
        h = hashlib.sha256()
        with open(file_path, "rb") as f:
            while chunk := f.read(8192):
                h.update(chunk)
        return h.hexdigest()

    @staticmethod
    def _get_rule_info_from_source(file_path: Path) -> tuple[str, str, str]:
        """
        Parses a Python source file to extract rule_key, rule_name, and rule_desc
        using regular expressions, avoiding module import issues.

        Returns:
            tuple[str, str, str]: (rule_key, rule_name, rule_desc)
        """
        rule_key, rule_name, rule_desc = "", "", ""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

                # 提取 rule_key
                key_match = re.search(r"^\s*rule_key\s*=\s*[\"'](.+?)[\"']", content, re.MULTILINE)
                if key_match:
                    rule_key = key_match.group(1)

                # 提取 rule_name
                name_match = re.search(r"^\s*rule_name\s*=\s*[\"'](.+?)[\"']", content, re.MULTILINE)
                if name_match:
                    rule_name = name_match.group(1)

                # 提取 rule_desc - 支持多行字符串格式
                # 匹配格式: rule_desc = """...""" 或 rule_desc = "..."
                desc_pattern = r'^\s*rule_desc\s*=\s*(?:"""(.*?)"""|"([^"]*)")'
                desc_match = re.search(desc_pattern, content, re.MULTILINE | re.DOTALL)

                if desc_match:
                    # 优先使用三引号内的内容，否则使用单引号内的内容
                    rule_desc = desc_match.group(1) or desc_match.group(2)
                    if rule_desc:
                        # 清理描述内容：去除首尾空白，但保留内部格式
                        rule_desc = rule_desc.strip()
                        logger.info(f"规则子类中定义的规则描述内容: {rule_desc}")

        except Exception as e:
            logger.error(f"Could not read or parse source file {file_path}: {e}")

        return rule_key, rule_name, rule_desc

    @staticmethod
    def _get_rule_class_info(module_path: str) -> tuple[type[BaseRuleLogic] | None, str, str]:
        """Dynamically imports a module and finds the rule class."""
        module = importlib.import_module(module_path)
        importlib.reload(module)  # Ensure the latest code is loaded

        for _, obj in inspect.getmembers(module, inspect.isclass):
            if issubclass(obj, BaseRuleLogic) and obj is not BaseRuleLogic:
                # Found the specific rule class
                rule_key = getattr(obj, "rule_key", None)
                rule_name = getattr(obj, "rule_name", "Unnamed Rule")
                return obj, rule_key, rule_name
        return None, "", ""
